'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Trash2, Edit, Plus } from 'lucide-react'

interface ApiToken {
  id: string
  platform: string
  token_name: string
  token_content: string
  created_at: string
}

export default function SettingsPage() {
  const { user, signOut } = useAuth()
  const [tokens, setTokens] = useState<ApiToken[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingToken, setEditingToken] = useState<ApiToken | null>(null)
  const [formData, setFormData] = useState({
    platform: '',
    token_name: '',
    token_content: ''
  })
  const [error, setError] = useState('')

  useEffect(() => {
    if (user) {
      fetchTokens()
    }
  }, [user])

  const fetchTokens = async () => {
    try {
      const { data, error } = await supabase
        .from('api_tokens')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setTokens(data || [])
    } catch (error) {
      console.error('Error fetching tokens:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!formData.platform || !formData.token_name || !formData.token_content) {
      setError('请填写所有必填字段')
      return
    }

    try {
      if (editingToken) {
        // 更新现有token
        const { error } = await supabase
          .from('api_tokens')
          .update({
            platform: formData.platform,
            token_name: formData.token_name,
            token_content: formData.token_content,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingToken.id)

        if (error) throw error
      } else {
        // 创建新token
        const { error } = await supabase
          .from('api_tokens')
          .insert({
            user_id: user!.id,
            platform: formData.platform,
            token_name: formData.token_name,
            token_content: formData.token_content
          })

        if (error) throw error
      }

      setDialogOpen(false)
      setEditingToken(null)
      setFormData({ platform: '', token_name: '', token_content: '' })
      fetchTokens()
    } catch (error: any) {
      setError(error.message)
    }
  }

  const handleEdit = (token: ApiToken) => {
    setEditingToken(token)
    setFormData({
      platform: token.platform,
      token_name: token.token_name,
      token_content: token.token_content
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个API Token吗？')) return

    try {
      const { error } = await supabase
        .from('api_tokens')
        .delete()
        .eq('id', id)

      if (error) throw error
      fetchTokens()
    } catch (error: any) {
      setError(error.message)
    }
  }

  const openNewTokenDialog = () => {
    setEditingToken(null)
    setFormData({ platform: '', token_name: '', token_content: '' })
    setDialogOpen(true)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>加载中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 用户信息 */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle>用户信息</CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-600">邮箱</p>
                <p className="font-medium">{user?.email}</p>
              </div>
              <Button variant="outline" onClick={signOut}>
                退出登录
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* API Tokens */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>API Tokens</CardTitle>
                <CardDescription>
                  管理您的平台API令牌
                </CardDescription>
              </div>
              <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={openNewTokenDialog}>
                    <Plus className="w-4 h-4 mr-2" />
                    添加Token
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {editingToken ? '编辑API Token' : '添加API Token'}
                    </DialogTitle>
                    <DialogDescription>
                      填写API Token信息
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="platform">平台</Label>
                      <Select 
                        value={formData.platform} 
                        onValueChange={(value) => setFormData({...formData, platform: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择平台" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="WB">Wildberries</SelectItem>
                          <SelectItem value="Ozon">Ozon</SelectItem>
                          <SelectItem value="Amazon">Amazon</SelectItem>
                          <SelectItem value="Other">其他</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="token_name">Token名称</Label>
                      <Input
                        id="token_name"
                        placeholder="输入Token名称"
                        value={formData.token_name}
                        onChange={(e) => setFormData({...formData, token_name: e.target.value})}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="token_content">Token内容</Label>
                      <Input
                        id="token_content"
                        type="password"
                        placeholder="输入Token内容"
                        value={formData.token_content}
                        onChange={(e) => setFormData({...formData, token_content: e.target.value})}
                        required
                      />
                    </div>
                    {error && (
                      <div className="text-red-500 text-sm">{error}</div>
                    )}
                    <div className="flex justify-end space-x-2">
                      <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                        取消
                      </Button>
                      <Button type="submit">
                        {editingToken ? '更新' : '添加'}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            {tokens.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                暂无API Token，点击上方按钮添加
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>平台</TableHead>
                    <TableHead>Token名称</TableHead>
                    <TableHead>Token内容</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tokens.map((token) => (
                    <TableRow key={token.id}>
                      <TableCell>{token.platform}</TableCell>
                      <TableCell>{token.token_name}</TableCell>
                      <TableCell>
                        <span className="font-mono text-sm">
                          {'*'.repeat(Math.min(token.token_content.length, 20))}
                        </span>
                      </TableCell>
                      <TableCell>
                        {new Date(token.created_at).toLocaleDateString('zh-CN')}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(token)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(token.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
