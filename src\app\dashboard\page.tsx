'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase, createClientComponentClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts'
import { Calendar, Download, Plus, Settings } from 'lucide-react'
import Link from 'next/link'

interface SalesReport {
  id: string
  date: string
  platform: string
  orders_count: number
  sales_amount: number
  ad_spend: number
  created_at: string
}

export default function DashboardPage() {
  const { user, signOut } = useAuth()
  const [reports, setReports] = useState<SalesReport[]>([])
  const [loading, setLoading] = useState(true)
  const [dateFilter, setDateFilter] = useState('7') // 默认显示最近7天
  const [platformFilter, setPlatformFilter] = useState('all')

  useEffect(() => {
    if (user) {
      fetchReports()
    }
  }, [user, dateFilter, platformFilter])

  const fetchReports = async () => {
    try {
      const browserClient = createClientComponentClient()
      let query = browserClient
        .from('sales_reports')
        .select('*')
        .order('date', { ascending: false })

      // 日期筛选
      if (dateFilter !== 'all') {
        const days = parseInt(dateFilter)
        const startDate = new Date()
        startDate.setDate(startDate.getDate() - days)
        query = query.gte('date', startDate.toISOString().split('T')[0])
      }

      // 平台筛选
      if (platformFilter !== 'all') {
        query = query.eq('platform', platformFilter)
      }

      const { data, error } = await query

      if (error) throw error
      setReports(data || [])
    } catch (error) {
      console.error('Error fetching reports:', error)
    } finally {
      setLoading(false)
    }
  }

  // 生成示例数据的函数
  const generateSampleData = async () => {
    if (!user) return

    const sampleData = []
    const platforms = ['WB', 'Ozon', 'Amazon']
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      
      for (const platform of platforms) {
        sampleData.push({
          user_id: user.id,
          date: date.toISOString().split('T')[0],
          platform,
          orders_count: Math.floor(Math.random() * 50) + 10,
          sales_amount: Math.floor(Math.random() * 10000) + 1000,
          ad_spend: Math.floor(Math.random() * 1000) + 100
        })
      }
    }

    try {
      const browserClient = createClientComponentClient()
      const { error } = await browserClient
        .from('sales_reports')
        .insert(sampleData)

      if (error) throw error
      fetchReports()
    } catch (error) {
      console.error('Error generating sample data:', error)
    }
  }

  // 计算汇总数据
  const totalOrders = reports.reduce((sum, report) => sum + report.orders_count, 0)
  const totalSales = reports.reduce((sum, report) => sum + report.sales_amount, 0)
  const totalAdSpend = reports.reduce((sum, report) => sum + report.ad_spend, 0)
  const profit = totalSales - totalAdSpend

  // 准备图表数据
  const chartData = reports.reduce((acc: any[], report) => {
    const existingDate = acc.find(item => item.date === report.date)
    if (existingDate) {
      existingDate.orders += report.orders_count
      existingDate.sales += report.sales_amount
      existingDate.adSpend += report.ad_spend
    } else {
      acc.push({
        date: report.date,
        orders: report.orders_count,
        sales: report.sales_amount,
        adSpend: report.ad_spend
      })
    }
    return acc
  }, []).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

  // 导出CSV
  const exportToCSV = () => {
    const headers = ['日期', '平台', '订单数', '销售额', '广告费']
    const csvContent = [
      headers.join(','),
      ...reports.map(report => [
        report.date,
        report.platform,
        report.orders_count,
        report.sales_amount,
        report.ad_spend
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `销售报表_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>加载中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">销售报表</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">{user?.email}</span>
              <Link href="/settings">
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  设置
                </Button>
              </Link>
              <Button variant="outline" size="sm" onClick={signOut}>
                退出
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 筛选器 */}
        <div className="mb-6 flex flex-wrap gap-4 items-center">
          <div className="flex items-center space-x-2">
            <Label htmlFor="dateFilter">时间范围:</Label>
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">最近7天</SelectItem>
                <SelectItem value="30">最近30天</SelectItem>
                <SelectItem value="90">最近90天</SelectItem>
                <SelectItem value="all">全部</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Label htmlFor="platformFilter">平台:</Label>
            <Select value={platformFilter} onValueChange={setPlatformFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="WB">Wildberries</SelectItem>
                <SelectItem value="Ozon">Ozon</SelectItem>
                <SelectItem value="Amazon">Amazon</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button onClick={exportToCSV} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            导出CSV
          </Button>

          {reports.length === 0 && (
            <Button onClick={generateSampleData}>
              <Plus className="w-4 h-4 mr-2" />
              生成示例数据
            </Button>
          )}
        </div>

        {/* 汇总卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">总订单数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalOrders.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">总销售额</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ¥{totalSales.toLocaleString()}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">总广告费</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                ¥{totalAdSpend.toLocaleString()}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">净利润</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ¥{profit.toLocaleString()}
              </div>
            </CardContent>
          </Card>
        </div>

        {reports.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-gray-500 mb-4">暂无销售数据</div>
              <Button onClick={generateSampleData}>
                <Plus className="w-4 h-4 mr-2" />
                生成示例数据
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* 图表 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle>销售额趋势</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="sales" stroke="#8884d8" name="销售额" />
                      <Line type="monotone" dataKey="adSpend" stroke="#82ca9d" name="广告费" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>订单数趋势</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="orders" fill="#8884d8" name="订单数" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 数据表格 */}
            <Card>
              <CardHeader>
                <CardTitle>详细数据</CardTitle>
                <CardDescription>
                  显示 {reports.length} 条记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>日期</TableHead>
                      <TableHead>平台</TableHead>
                      <TableHead>订单数</TableHead>
                      <TableHead>销售额</TableHead>
                      <TableHead>广告费</TableHead>
                      <TableHead>利润</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reports.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>{report.date}</TableCell>
                        <TableCell>{report.platform}</TableCell>
                        <TableCell>{report.orders_count.toLocaleString()}</TableCell>
                        <TableCell className="text-green-600">
                          ¥{report.sales_amount.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-red-600">
                          ¥{report.ad_spend.toLocaleString()}
                        </TableCell>
                        <TableCell className={report.sales_amount - report.ad_spend >= 0 ? 'text-green-600' : 'text-red-600'}>
                          ¥{(report.sales_amount - report.ad_spend).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  )
}
