import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          // 设置请求cookie
          req.cookies.set({
            name,
            value,
            ...options,
          })
          // 创建新的响应并设置cookie
          response = NextResponse.next({
            request: {
              headers: req.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          // 删除请求cookie
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          // 创建新的响应并删除cookie
          response = NextResponse.next({
            request: {
              headers: req.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  const {
    data: { session },
  } = await supabase.auth.getSession()

  console.log('Middleware - Path:', req.nextUrl.pathname, 'Session:', !!session)

  // 如果用户未登录且访问受保护的路由，重定向到登录页
  if (!session && (req.nextUrl.pathname.startsWith('/dashboard') || req.nextUrl.pathname.startsWith('/settings'))) {
    console.log('Redirecting to login - no session')
    return NextResponse.redirect(new URL('/login', req.url))
  }

  // 如果用户已登录且访问登录/注册页，重定向到仪表板
  if (session && (req.nextUrl.pathname === '/login' || req.nextUrl.pathname === '/register')) {
    console.log('Redirecting to dashboard - has session')
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  // 如果访问根路径，重定向到仪表板或登录页
  if (req.nextUrl.pathname === '/') {
    if (session) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    } else {
      return NextResponse.redirect(new URL('/login', req.url))
    }
  }

  return response
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
}
