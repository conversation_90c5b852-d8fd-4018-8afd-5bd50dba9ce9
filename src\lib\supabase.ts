import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// 客户端Supabase客户端 - 用于浏览器环境
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// 兼容性导出 - 保持向后兼容
export const createClientComponentClient = () => createBrowserClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      api_tokens: {
        Row: {
          id: string
          user_id: string
          platform: string
          token_name: string
          token_content: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          platform: string
          token_name: string
          token_content: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          platform?: string
          token_name?: string
          token_content?: string
          created_at?: string
          updated_at?: string
        }
      }
      sales_reports: {
        Row: {
          id: string
          user_id: string
          date: string
          platform: string
          orders_count: number
          sales_amount: number
          ad_spend: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          date: string
          platform: string
          orders_count: number
          sales_amount: number
          ad_spend: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          date?: string
          platform?: string
          orders_count?: number
          sales_amount?: number
          ad_spend?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
